<div id="preview-order-el">
  <?php echo csrf_field(); ?>
  <?php if (isset($component)) { $__componentOriginal177747aa15555cfd5c65fff65d1d67bbd7446e58 = $component; } ?>
<?php $component = App\View\Components\LgModal::resolve([] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? (array) $attributes->getIterator() : [])); ?>
<?php $component->withName('lg-modal'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag && $constructor = (new ReflectionClass(App\View\Components\LgModal::class))->getConstructor()): ?>
<?php $attributes = $attributes->except(collect($constructor->getParameters())->map->getName()->all()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
     <?php $__env->slot('class', null, []); ?> preview-order-modal <?php $__env->endSlot(); ?>
     <?php $__env->slot('title', null, []); ?> Sale <?php $__env->endSlot(); ?>

      <div style="text-transform: uppercase!important;">

        <div class="order-print print" id="order-print" v-if="order"  style="max-width: 400px; margin: auto;">

          <div class="mb-4" style="font-family: benton-sans,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji'; font-size: 11px;">
            <!-- Avatar -->
            <center>
              <!-- <div class="avatar avatar-xl avatar-circle avatar-centered mb-3"> -->
              <div class="">
                <img width="50" src="<?php echo e(auth()->user()->getGlobalInstance('logo')->path ?? asset('assets/svg/logos/logo-short.svg')); ?>" alt="Logo">
              </div>

              <div class="text-center">
                <span><?php echo e(auth()->user()->getGlobal("business") ?? ''); ?></span>
              </div>           
              <div class="text-center">
                <span><?php echo e(auth()->user()->getGlobal("address") ?? ''); ?></span>
              </div>                    
              <div class="text-center">
                <span><?php echo e(auth()->user()->getGlobal("email") ?? ''); ?></span>
              </div>              
              <div class="text-center">
                <span><?php echo e(auth()->user()->getGlobal("phone") ?? ''); ?></span>
              </div>

            </center>
            <!-- End Avatar -->

            <hr style="margin:5px">

            <div>
              <span class="text-uppercase"> DATE: </span>
              <span style="float: right;" v-text="order.created_at"></span>
            </div>     
            <div>
              <span class="text-uppercase"> CUSTOMER:  </span>
              <span style="float: right;" v-text="order.user_name"></span>
            </div>      
            <div>
              <span class="text-uppercase"> RECEIPT #: </span>
              <span style="float: right;" v-text="order.order_id"></span>
            </div>
            <hr style="margin:5px">

            <table style=" " cellpadding="0" cellspacing="0" width="100%">
              <tr>
                <th style="text-align: left;">DESC</th>
                <th style="text-align: center;">QTY x UN PRICE</th>
                <th style="text-align: right;">AMOUNT</th>
              </tr>
              <tr v-for="(sale, index) in order.sales">
                <td style="text-align: left; font-family: benton-sans,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji'; font-size: 11px;">
                  <span v-text="sale.item.name"></span> <br>
                  <i v-text="sale.item.reference_number"></i>
                </td>
         
                <td style="text-align:center;">
                  <span v-text="sale.quantity"></span> <i>x</i> <span v-text="formatNumber(sale.selling_price)"></span>
                </td>
                <td style="text-align: right; font-family: benton-sans,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji'; font-size: 11px;">
                  <span v-text="formatNumber(sale.selling_price * sale.quantity)"></span>
                </td>
              </tr>
            </table>  



            <hr style="margin:5px">                 

            <table border="0" cellpadding="0" cellspacing="0" width="100%">

              <tr>
                <td align="left" width="75%" style=" font-family: benton-sans,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji'; font-size: 11px;"><strong>SUB TOTAL</strong></td>
                <td align="right" width="25%" style=" font-family: benton-sans,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji'; font-size: 11px;"><strong v-text="formatNumber(order.sub_total)"></strong></td>
              </tr>    

              <tr>
                <td align="left" width="75%" style="font-family: benton-sans,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji'; font-size: 11px;"><strong>DISCOUNT</strong></td>
                <td align="right" width="25%" style="font-family: benton-sans,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji'; font-size: 11px;">-<strong v-text="formatNumber(order.discount)"></strong></td>
              </tr> 


              <tr>
                <td align="left" width="75%" style="font-family: benton-sans,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji'; font-size: 11px;"><strong>TOTAL PAID</strong></td>
                <td align="right" width="25%" style="font-family: benton-sans,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji'; font-size: 11px;"><strong v-text="formatNumber(order.amount_total)"></strong></td>
              </tr>


              <tr>
                <td align="left" width="75%" style=" font-family: benton-sans,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji'; font-size: 11px;"><strong>CASH PAID</strong></td>
                <td align="right" width="25%" style=" font-family: benton-sans,-apple-system,BlinkMacSystemFont,'Segoe UI',Roboto,'Helvetica Neue',Arial,'Noto Sans',sans-serif,'Apple Color Emoji','Segoe UI Emoji','Segoe UI Symbol','Noto Color Emoji'; font-size: 11px;"><strong v-text="formatNumber(order.amount_paid)"></strong></td>
              </tr> 

            </table>

            <hr style="margin:5px">
            <div>
              <span class="text-uppercase"> Operator </span>
              <span style="float: right;" v-text="order?.prepared_by"></span>
            </div>            
            <hr style="margin:5px">
            <div style="text-align:center;">
              <span>END OF RECEIPT</span>
            </div>


          </div>

         </div>

         <div v-else>
            <h1 class="center">Loading...</h1>
         </div>
      </div>


     <?php $__env->slot('footer', null, []); ?> 
      <div v-if="order">
          <button class="btn btn-sm btn-soft-primary"  onclick="extractPrint('order-print')" href="javascript:;">
            <i class="bi-download me-1"></i> PRINT
          </button>                             
          <a :href="'/orders/' + order.id " class="btn btn-sm btn-soft-primary">
            <i class="bi-view me-1"></i> MORE
          </a>   
      </div>
     <?php $__env->endSlot(); ?>
   <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal177747aa15555cfd5c65fff65d1d67bbd7446e58)): ?>
<?php $component = $__componentOriginal177747aa15555cfd5c65fff65d1d67bbd7446e58; ?>
<?php unset($__componentOriginal177747aa15555cfd5c65fff65d1d67bbd7446e58); ?>
<?php endif; ?>
</div>



<?php $__env->startPush('scripts'); ?>

  <script type="text/javascript">
    
    new Vue({
      el: "#preview-order-el",
      data(){
        return{
          order: null,
        }
      },

      methods: {

        getOrder(id) {
          axios.get('/ajax-order/' + id).then( res => {
          	this.order = res.data;
          	// setTimeout( ()=> { window.extractPrint('order-print'); }, 2000)
          }) 
        },

        formatNumber (num) {
            return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,")
        },

      },


      mounted(){
      	$(".preview-order-btn").on("click", (e) => {
      		var id = $(e.target).attr("data-id");
      		this.getOrder(id);
      	})
      },

      created(){
        $("body").on("click", ".preview-order-btn", (e) => {
          var id = $(e.target).attr("data-id");
          this.getOrder(id);
        })
      }

    });

  </script>

<?php $__env->stopPush(); ?>
<?php /**PATH C:\xampp\htdocs\vtigo\alt\vertigo-ams\resources\views/modals/order.blade.php ENDPATH**/ ?>