@php $editing = isset($order) @endphp

<div id="order-el" class="space-y-6">
  <!-- Item Selection and Customer -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Add Item Section -->
    <div class="bg-gray-50 rounded-xl p-6 border border-gray-200">
      <div class="flex items-center mb-4">
        <div class="h-8 w-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
          <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-gray-900">Add Item</h3>
      </div>
      <select class="w-full px-4 py-3 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200"
              @change="addItem($event)" 
              data-hs-tom-select-options='{
                "placeholder": "Select Item..."
              }'>
        <option value="" selected disabled>Select new Item</option>
        <option v-for="(item, i) in items" :value="item.id" v-text="getName(item)"></option>
      </select>
      <p class="text-xs text-gray-500 mt-2">Choose items from your available inventory</p>
    </div>

    <!-- Customer Selection -->
    <div class="bg-gray-50 rounded-xl p-6 border border-gray-200">
      <div class="flex items-center mb-4">
        <div class="h-8 w-8 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center mr-3">
          <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
          </svg>
        </div>
        <h3 class="text-lg font-semibold text-gray-900">Customer</h3>
      </div>
      <select class="w-full px-4 py-3 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200"
              autocomplete="on" 
              name="user_id" 
              required 
              data-hs-tom-select-options='{
                "create": true,
                "placeholder": "Create New..."
              }'>
        @php $selected = $editing && $order->user_id ? $order->user_id: '' @endphp
        <option value="2" selected>Walk-in customer</option>
        @foreach( App\Models\User::get() as $user)
        <option value="{{ $user->id }}" {{ $user->id == $selected ? 'selected' : '' }}>
          {{ $user->name ?? ''}}
        </option>
        @endforeach
      </select>
      <p class="text-xs text-gray-500 mt-2">Select existing customer or create new</p>
    </div>
  </div>

  <!-- Items Table -->
  <div class="bg-white rounded-xl shadow-sm border border-gray-100 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200 bg-gradient-to-r from-gray-50 to-white">
      <h3 class="text-lg font-semibold text-gray-900 flex items-center">
        <div class="h-8 w-8 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-3">
          <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
          </svg>
        </div>
        Sale Items
      </h3>
    </div>

    <div class="overflow-x-auto" style="min-height: 300px;">
      <table class="w-full">
        <thead class="bg-gray-50 border-b border-gray-200">
          <tr>
            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item</th>
            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Discount</th>
            <th class="px-6 py-4 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Selling Price</th>
            <th class="px-6 py-4 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
            <th class="px-6 py-4 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
          </tr>
        </thead>
        <tbody class="bg-white divide-y divide-gray-200">
          <tr v-for="(sale, index) in order.sales" :key="index" class="hover:bg-gray-50 transition-colors duration-200">
            <td class="px-6 py-4">
              <div class="flex items-center">
                <div class="h-10 w-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center mr-3">
                  <span class="text-white font-bold text-sm">{{ getName(sale.item).charAt(0).toUpperCase() }}</span>
                </div>
                <div>
                  <div class="text-sm font-medium text-gray-900" v-text="getName(sale.item)"></div>
                  <div class="text-xs text-gray-500">SKU: {{ sale.item?.sku || 'N/A' }}</div>
                </div>
              </div>
            </td>
            <td class="px-6 py-4">
              <div class="relative">
                <input type="number" 
                       name="discounts[]" 
                       min="0" 
                       step="0.01" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200"
                       v-model.lazy="sale.discount" 
                       @change="calculate" 
                       :max="sale?.amount" 
                       required>
                <input type="hidden" :value="order.discount" name="discount">
                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <span class="text-gray-500 text-xs">MK</span>
                </div>
              </div>
            </td>
            <td class="px-6 py-4">
              <div class="relative">
                <input type="number" 
                       name="selling_prices[]" 
                       min="0" 
                       step="0.01" 
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200"
                       v-model.lazy="sale.selling_price" 
                       @change="calculate" 
                       required>
                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <span class="text-gray-500 text-xs">MK</span>
                </div>
              </div>
            </td>
            <td class="px-6 py-4 text-right">
              <div class="text-sm font-bold text-green-600">
                MK {{ formatNumber(eval(sale.selling_price - sale.discount)) }}
              </div>
            </td>
            <td class="px-6 py-4 text-center">
              <button type="button" 
                      @click="removeItem(index)" 
                      class="inline-flex items-center px-3 py-1.5 border border-red-300 text-xs font-medium rounded-lg text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 transition-colors duration-200">
                <svg class="h-3 w-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                </svg>
                Remove
              </button>
            </td>
          </tr>
          <tr v-if="order.sales.length === 0">
            <td colspan="5" class="px-6 py-12 text-center">
              <div class="flex flex-col items-center">
                <div class="h-16 w-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                  <svg class="h-8 w-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                  </svg>
                </div>
                <p class="text-gray-500 text-lg font-medium mb-2">No items added</p>
                <p class="text-gray-400">Select items from the dropdown above to add them to this sale</p>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>

  <!-- Order Summary -->
  <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- Order Totals -->
    <div class="lg:col-span-2 bg-gradient-to-r from-gray-50 to-gray-100 rounded-xl p-6 border border-gray-200">
      <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
        <div class="h-8 w-8 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center mr-3">
          <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h8m0 0v8m0-8l-8 8-4-4-6 6"></path>
          </svg>
        </div>
        Order Summary
      </h3>
      
      <div class="space-y-3">
        <div class="flex justify-between items-center py-2 border-b border-gray-300">
          <span class="text-sm font-medium text-gray-600">Subtotal:</span>
          <span class="text-sm font-bold text-gray-900">MK {{ formatNumber(order.sub_total) }}</span>
        </div>
        <div class="flex justify-between items-center py-2 border-b border-gray-300">
          <span class="text-sm font-medium text-gray-600">Total Discount:</span>
          <span class="text-sm font-bold text-blue-600">MK {{ formatNumber(order.discount) }}</span>
        </div>
        <div class="flex justify-between items-center py-3 border-t-2 border-gray-400">
          <span class="text-lg font-bold text-gray-900">Total Amount:</span>
          <span class="text-xl font-bold text-green-600">MK {{ formatNumber(order.amount_total) }}</span>
        </div>
      </div>
    </div>

    <!-- Payment Information -->
    <div class="bg-white rounded-xl p-6 border border-gray-200 shadow-sm">
      <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
        <div class="h-8 w-8 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center mr-3">
          <svg class="h-4 w-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
          </svg>
        </div>
        Payment
      </h3>
      
      <div class="space-y-4">
        <div>
          <label class="block text-sm font-medium text-gray-700 mb-2">Amount Paid</label>
          <div class="relative">
            <input type="number" 
                   name="amount_paid" 
                   min="0" 
                   step="0.01" 
                   class="w-full px-4 py-3 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-colors duration-200"
                   v-model.lazy="order.amount_paid" 
                   placeholder="0.00">
            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <span class="text-gray-500 text-sm">MK</span>
            </div>
          </div>
        </div>
        
        <div class="bg-gray-50 rounded-lg p-3">
          <div class="flex justify-between items-center text-sm">
            <span class="text-gray-600">Balance Due:</span>
            <span class="font-bold" :class="(order.amount_total - order.amount_paid) > 0 ? 'text-red-600' : 'text-green-600'">
              MK {{ formatNumber(order.amount_total - order.amount_paid) }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Hidden field for order data -->
  <textarea name="order" :value="JSON.stringify(order)" class="sr-only"></textarea>
</div>

@push('scripts')
<script type="text/javascript">
new Vue({
  el: "#order-el",
  data(){
    return{
      isReady: false,
      order: {
        sales: [],
        sub_total: 0,
        amount_total: 0,
        amount_paid: 0,
        supplier_id: null,
        discount: 0,
        vat: 0,
      },
      vat: @json( auth()->user()->getGlobal('vat') ?? 0),
      units: [],
      items: @json( $items),
    }
  },

  methods: {
    getorder(){
      if( ! this.order.id) { return false; }
      axios.get('/order-ajax/' + this.order.id).then( res => {
        this.order = res.data;
      });
    },

    save(){
      axios.post('/save-order', this.order).then( res => {
        this.order = res.data;
      });
    },

    removeItem(index){
      this.order.sales.splice(index, 1);
      this.calculate();
    },

    getReady(){
      this.order.sub_total = 0;
      this.order.amount_total = 0;
      this.order.discount = 0;

      for( var sale of this.order.sales){
        var amount = eval(sale.quantity * sale.selling_price );
        this.order.discount += Number( sale.discount );
        this.order.sub_total +=  eval(sale.quantity * sale.selling_price);
      }

      this.order.amount_total = eval(this.order.sub_total - this.order.discount ).toFixed(2);
      this.order.sub_total = this.order.sub_total.toFixed(2);
      this.order.discount = this.order.discount.toFixed(2);
      this.order.amount_paid = this.order.amount_total;
      this.isReady = true;
    },

    addItem(e){
      var item_id = e.target.value;
      var check = this.order.sales.filter( item => item.item_id == item_id);
      if( ! item_id || check.length > 0 ) return;

      var item = this.items.filter( item => item.id == item_id)[0];
      if( ! item ) return;

      var sale = {
        item_id: item.id,
        item: item,
        quantity: 1,
        selling_price: item.selling_price ?? 0,
        discount: 0,
        vat: 0,
      };

      this.order.sales.push(sale);
      this.calculate();
      e.target.value = "";
    },

    calculate(){
      this.getReady();
    },

    getName(item){
      if( ! item ) return '';
      return item.name + ' (' + item.category?.name + ')';
    },

    formatNumber(num) {
      if (!num) return '0';
      return num.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g, "$1,");
    },
  },

  mounted(){
    @if($editing)
      this.order = @json($order);
      this.order.sales = @json($order->sales()->with('item')->get());
      this.calculate();
    @endif
    this.getReady();
  },

  created(){
    // Initialize any additional setup here
  }
});
</script>
@endpush
