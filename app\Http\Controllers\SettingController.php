<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\ActiveConfig;
use App\Models\User;

class SettingController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {

        $this->authorize('view-any', User::class);

        $settings = ActiveConfig::search($request->search)
            ->latest()
            ->whereNull("user_id")->get();

        return view('app.settings.index', compact('settings'));    
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        $params = $request->validate([
            'key' => 'required',
            'value' => 'required',
        ]);
        $params['key'] = _keyText($request->key );
        $activeConfig = ActiveConfig::updateOrCreate($params);
        if(request()->media && $activeConfig) {
            $activeConfig->clearMediaCollection('media');
            if(request()->media) $activeConfig->addMedia(request()->media)->toMediaCollection("media");
        }


        return redirect()->back();
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\Response
     */
    public function destroy(Request $request, $id)
    {
        $activeConfig =  ActiveConfig::where("id", $id)->first();
        if( $activeConfig) {
            $activeConfig->clearMediaCollection('media');
            $activeConfig->delete();
        }
        return redirect()->back();
    }

    // Modernized Admin Methods

    /**
     * Modernized admin index view for settings
     */
    public function modernizedIndex(Request $request)
    {
        $this->authorize('view-any', User::class);

        $settings = ActiveConfig::search($request->search)
            ->latest()
            ->whereNull("user_id")
            ->get();

        return view('admin.modernized-settings.index', compact('settings'));
    }

    /**
     * Modernized admin store method for settings
     */
    public function modernizedStore(Request $request)
    {
        $this->authorize('view-any', User::class);

        $params = $request->validate([
            'key' => 'required',
            'value' => 'required',
        ]);

        $params['key'] = _keyText($request->key);
        $activeConfig = ActiveConfig::updateOrCreate($params);

        if(request()->media && $activeConfig) {
            $activeConfig->clearMediaCollection('media');
            if(request()->media) {
                $activeConfig->addMedia(request()->media)->toMediaCollection("media");
            }
        }

        return redirect()
            ->route('admin.modernized.settings.index')
            ->withSuccess(__('crud.common.created'));
    }

    /**
     * Modernized admin destroy method for settings
     */
    public function modernizedDestroy(Request $request, $id)
    {
        $this->authorize('view-any', User::class);

        $activeConfig = ActiveConfig::where("id", $id)->first();
        if($activeConfig) {
            $activeConfig->clearMediaCollection('media');
            $activeConfig->delete();
        }

        return redirect()
            ->route('admin.modernized.settings.index')
            ->withSuccess(__('crud.common.removed'));
    }
}
